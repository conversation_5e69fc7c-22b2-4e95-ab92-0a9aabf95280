document.addEventListener('DOMContentLoaded', function() {
    // Device connection settings
    let printerSettings = {
        ip: '*************',
        port: 9100,
        timeout: 5,
        connected: false
    };

    let readerSettings = {
        ip: '*************',
        port: 8080,
        timeout: 5,
        connected: false
    };

    // Load settings from localStorage
    const savedPrinterSettings = localStorage.getItem('printerSettings');
    const savedReaderSettings = localStorage.getItem('readerSettings');

    if (savedPrinterSettings) {
        printerSettings = { ...printerSettings, ...JSON.parse(savedPrinterSettings) };
    }

    if (savedReaderSettings) {
        readerSettings = { ...readerSettings, ...JSON.parse(savedReaderSettings) };
    }

    // DOM elements
    const connectPrinterBtn = document.getElementById('connectPrinterBtn');
    const connectReaderBtn = document.getElementById('connectReaderBtn');
    const printerSettingsBtn = document.getElementById('printerSettingsBtn');
    const readerSettingsBtn = document.getElementById('readerSettingsBtn');

    // Debug: Check if elements exist
    console.log('connectPrinterBtn:', connectPrinterBtn);
    console.log('connectReaderBtn:', connectReaderBtn);
    console.log('printerSettingsBtn:', printerSettingsBtn);
    console.log('readerSettingsBtn:', readerSettingsBtn);

    // Printer connection
    if (connectPrinterBtn) {
        connectPrinterBtn.addEventListener('click', function() {
            if (printerSettings.connected) {
                disconnectPrinter();
            } else {
                connectToPrinter();
            }
        });
    }

    async function connectToPrinter() {
        connectPrinterBtn.disabled = true;
        connectPrinterBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Bağlanıyor...';

        try {
            // Test real connection
            const success = await testConnection(printerSettings.ip, printerSettings.port);

            if (success) {
                printerSettings.connected = true;
                updatePrinterStatus(true, `Bağlı: ${printerSettings.ip}:${printerSettings.port}`);
                connectPrinterBtn.innerHTML = '<i class="fas fa-unlink"></i> Bağlantıyı Kes';
                connectPrinterBtn.classList.remove('btn-secondary');
                connectPrinterBtn.classList.add('connected');
                showNotification('RFID yazıcı bağlantısı kuruldu.', 'success');
            } else {
                printerSettings.connected = false;
                updatePrinterStatus(false, 'Bağlantı Hatası');
                connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
                showNotification(`RFID yazıcı bağlantısı kurulamadı: ${printerSettings.ip}:${printerSettings.port}`, 'error');
            }
        } catch (error) {
            printerSettings.connected = false;
            updatePrinterStatus(false, 'Bağlantı Hatası');
            connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
            showNotification(`Bağlantı hatası: ${error.message}`, 'error');
        }

        connectPrinterBtn.disabled = false;
    }

    function disconnectPrinter() {
        printerSettings.connected = false;
        updatePrinterStatus(false, 'Bağlı Değil');
        connectPrinterBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
        connectPrinterBtn.classList.remove('connected');
        connectPrinterBtn.classList.add('btn-secondary');
        showNotification('RFID yazıcı bağlantısı kesildi.', 'info');
    }

    // Reader connection
    if (connectReaderBtn) {
        connectReaderBtn.addEventListener('click', function() {
            if (readerSettings.connected) {
                disconnectReader();
            } else {
                connectToReader();
            }
        });
    }

    async function connectToReader() {
        connectReaderBtn.disabled = true;
        connectReaderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Bağlanıyor...';

        try {
            // Test real connection
            const success = await testConnection(readerSettings.ip, readerSettings.port);

            if (success) {
                readerSettings.connected = true;
                updateReaderStatus(true, `Bağlı: ${readerSettings.ip}:${readerSettings.port}`);
                connectReaderBtn.innerHTML = '<i class="fas fa-unlink"></i> Bağlantıyı Kes';
                connectReaderBtn.classList.remove('btn-secondary');
                connectReaderBtn.classList.add('connected');
                showNotification('RFID okuyucu bağlantısı kuruldu.', 'success');
            } else {
                readerSettings.connected = false;
                updateReaderStatus(false, 'Bağlantı Hatası');
                connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
                showNotification(`RFID okuyucu bağlantısı kurulamadı: ${readerSettings.ip}:${readerSettings.port}`, 'error');
            }
        } catch (error) {
            readerSettings.connected = false;
            updateReaderStatus(false, 'Bağlantı Hatası');
            connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
            showNotification(`Bağlantı hatası: ${error.message}`, 'error');
        }

        connectReaderBtn.disabled = false;
    }

    function disconnectReader() {
        readerSettings.connected = false;
        updateReaderStatus(false, 'Bağlı Değil');
        connectReaderBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlan';
        connectReaderBtn.classList.remove('connected');
        connectReaderBtn.classList.add('btn-secondary');
        showNotification('RFID okuyucu bağlantısı kesildi.', 'info');
    }

    // Status update functions
    function updatePrinterStatus(connected, statusText) {
        const statusLight = document.getElementById('printerStatus');
        const statusTextElement = document.getElementById('printerStatusText');

        if (connected) {
            statusLight.classList.remove('disconnected');
            statusLight.classList.add('connected');
        } else {
            statusLight.classList.remove('connected');
            statusLight.classList.add('disconnected');
        }

        statusTextElement.textContent = statusText;
    }

    function updateReaderStatus(connected, statusText) {
        const statusLight = document.getElementById('readerStatus');
        const statusTextElement = document.getElementById('readerStatusText');

        if (connected) {
            statusLight.classList.remove('disconnected');
            statusLight.classList.add('connected');
        } else {
            statusLight.classList.remove('connected');
            statusLight.classList.add('disconnected');
        }

        statusTextElement.textContent = statusText;
    }

    // Real connection test function for RFID devices
    async function testConnection(ip, port, timeout = 5000) {
        return new Promise((resolve) => {
            // Validate IP format first
            const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
            if (!ipRegex.test(ip)) {
                resolve(false);
                return;
            }

            // For RFID printers, we'll try multiple approaches
            const testMethods = [
                // Method 1: Try HTTP connection (some RFID printers have web interface)
                () => fetch(`http://${ip}:${port}`, {
                    method: 'GET',
                    mode: 'no-cors',
                    signal: AbortSignal.timeout(timeout)
                }),

                // Method 2: Try HTTPS connection
                () => fetch(`https://${ip}:${port}`, {
                    method: 'GET',
                    mode: 'no-cors',
                    signal: AbortSignal.timeout(timeout)
                }),

                // Method 3: Try to create a WebSocket connection (for TCP-like testing)
                () => new Promise((wsResolve, wsReject) => {
                    try {
                        const ws = new WebSocket(`ws://${ip}:${port}`);
                        ws.onopen = () => {
                            ws.close();
                            wsResolve(true);
                        };
                        ws.onerror = () => wsReject(new Error('WebSocket failed'));
                        ws.onclose = () => wsReject(new Error('WebSocket closed'));

                        setTimeout(() => {
                            ws.close();
                            wsReject(new Error('WebSocket timeout'));
                        }, timeout);
                    } catch (e) {
                        wsReject(e);
                    }
                })
            ];

            // Try each method
            let methodIndex = 0;

            function tryNextMethod() {
                if (methodIndex >= testMethods.length) {
                    resolve(false);
                    return;
                }

                testMethods[methodIndex]()
                    .then(() => {
                        resolve(true);
                    })
                    .catch((error) => {
                        // If we get a CORS error, it means the device is reachable
                        if (error.name === 'TypeError' &&
                            (error.message.includes('CORS') ||
                             error.message.includes('Failed to fetch'))) {
                            resolve(true);
                            return;
                        }

                        methodIndex++;
                        tryNextMethod();
                    });
            }

            tryNextMethod();

            // Overall timeout
            setTimeout(() => {
                resolve(false);
            }, timeout);
        });
    }

    // Settings modal functionality
    const printerSettingsModal = document.getElementById('printerSettingsModal');
    const readerSettingsModal = document.getElementById('readerSettingsModal');

    // Printer settings
    if (printerSettingsBtn) {
        printerSettingsBtn.addEventListener('click', function() {
            openPrinterSettings();
        });
    }

    function openPrinterSettings() {
        // Populate form with current settings
        document.getElementById('printerIP').value = printerSettings.ip;
        document.getElementById('printerPort').value = printerSettings.port;
        document.getElementById('printerTimeout').value = printerSettings.timeout;

        // Clear connection status
        document.getElementById('printerConnectionStatus').style.display = 'none';

        printerSettingsModal.style.display = 'flex';
    }

    // Reader settings
    if (readerSettingsBtn) {
        readerSettingsBtn.addEventListener('click', function() {
            openReaderSettings();
        });
    }

    function openReaderSettings() {
        // Populate form with current settings
        document.getElementById('readerIP').value = readerSettings.ip;
        document.getElementById('readerPort').value = readerSettings.port;
        document.getElementById('readerTimeout').value = readerSettings.timeout;

        // Clear connection status
        document.getElementById('readerConnectionStatus').style.display = 'none';

        readerSettingsModal.style.display = 'flex';
    }

    // Test connections
    const testPrinterBtn = document.getElementById('testPrinterConnectionBtn');
    const testReaderBtn = document.getElementById('testReaderConnectionBtn');

    if (testPrinterBtn) {
        testPrinterBtn.addEventListener('click', function() {
            testPrinterConnection();
        });
    }

    if (testReaderBtn) {
        testReaderBtn.addEventListener('click', function() {
            testReaderConnection();
        });
    }

    async function testPrinterConnection() {
        const ip = document.getElementById('printerIP').value;
        const port = document.getElementById('printerPort').value;
        const statusDiv = document.getElementById('printerConnectionStatus');
        const testBtn = document.getElementById('testPrinterConnectionBtn');

        if (!ip || !port) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = 'IP adresi ve port gerekli!';
            return;
        }

        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test ediliyor...';
        statusDiv.className = 'connection-status testing';
        statusDiv.textContent = `${ip}:${port} adresine bağlanılıyor...`;

        try {
            const success = await testConnection(ip, port, 5000);

            if (success) {
                statusDiv.className = 'connection-status success';
                statusDiv.textContent = `✓ Bağlantı başarılı! (${ip}:${port})`;
            } else {
                statusDiv.className = 'connection-status error';
                statusDiv.textContent = `✗ Bağlantı başarısız! (${ip}:${port})`;
            }
        } catch (error) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = `✗ Bağlantı hatası: ${error.message}`;
        }

        testBtn.disabled = false;
        testBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlantıyı Test Et';
    }

    async function testReaderConnection() {
        const ip = document.getElementById('readerIP').value;
        const port = document.getElementById('readerPort').value;
        const statusDiv = document.getElementById('readerConnectionStatus');
        const testBtn = document.getElementById('testReaderConnectionBtn');

        if (!ip || !port) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = 'IP adresi ve port gerekli!';
            return;
        }

        testBtn.disabled = true;
        testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Test ediliyor...';
        statusDiv.className = 'connection-status testing';
        statusDiv.textContent = `${ip}:${port} adresine bağlanılıyor...`;

        try {
            const success = await testConnection(ip, port, 5000);

            if (success) {
                statusDiv.className = 'connection-status success';
                statusDiv.textContent = `✓ Bağlantı başarılı! (${ip}:${port})`;
            } else {
                statusDiv.className = 'connection-status error';
                statusDiv.textContent = `✗ Bağlantı başarısız! (${ip}:${port})`;
            }
        } catch (error) {
            statusDiv.className = 'connection-status error';
            statusDiv.textContent = `✗ Bağlantı hatası: ${error.message}`;
        }

        testBtn.disabled = false;
        testBtn.innerHTML = '<i class="fas fa-plug"></i> Bağlantıyı Test Et';
    }

    // Save settings
    const savePrinterBtn = document.getElementById('savePrinterSettingsBtn');
    const saveReaderBtn = document.getElementById('saveReaderSettingsBtn');

    if (savePrinterBtn) {
        savePrinterBtn.addEventListener('click', function() {
            savePrinterSettings();
        });
    }

    if (saveReaderBtn) {
        saveReaderBtn.addEventListener('click', function() {
            saveReaderSettings();
        });
    }

    function savePrinterSettings() {
        const ip = document.getElementById('printerIP').value;
        const port = parseInt(document.getElementById('printerPort').value);
        const timeout = parseInt(document.getElementById('printerTimeout').value);

        if (!ip || !port || !timeout) {
            showNotification('Lütfen tüm alanları doldurun!', 'error');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        if (!ipRegex.test(ip)) {
            showNotification('Geçersiz IP adresi formatı!', 'error');
            return;
        }

        // Update settings
        printerSettings.ip = ip;
        printerSettings.port = port;
        printerSettings.timeout = timeout;

        // Save to localStorage
        localStorage.setItem('printerSettings', JSON.stringify(printerSettings));

        // Close modal
        printerSettingsModal.style.display = 'none';

        // Disconnect if connected (settings changed)
        if (printerSettings.connected) {
            disconnectPrinter();
        }

        showNotification('Yazıcı ayarları kaydedildi!', 'success');
    }

    function saveReaderSettings() {
        const ip = document.getElementById('readerIP').value;
        const port = parseInt(document.getElementById('readerPort').value);
        const timeout = parseInt(document.getElementById('readerTimeout').value);

        if (!ip || !port || !timeout) {
            showNotification('Lütfen tüm alanları doldurun!', 'error');
            return;
        }

        // Validate IP format
        const ipRegex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        if (!ipRegex.test(ip)) {
            showNotification('Geçersiz IP adresi formatı!', 'error');
            return;
        }

        // Update settings
        readerSettings.ip = ip;
        readerSettings.port = port;
        readerSettings.timeout = timeout;

        // Save to localStorage
        localStorage.setItem('readerSettings', JSON.stringify(readerSettings));

        // Close modal
        readerSettingsModal.style.display = 'none';

        // Disconnect if connected (settings changed)
        if (readerSettings.connected) {
            disconnectReader();
        }

        showNotification('Okuyucu ayarları kaydedildi!', 'success');
    }

    // Modal close functionality
    document.querySelectorAll('.close-modal').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            this.closest('.modal').style.display = 'none';
        });
    });

    const cancelPrinterBtn = document.getElementById('cancelPrinterSettingsBtn');
    const cancelReaderBtn = document.getElementById('cancelReaderSettingsBtn');

    if (cancelPrinterBtn) {
        cancelPrinterBtn.addEventListener('click', function() {
            printerSettingsModal.style.display = 'none';
        });
    }

    if (cancelReaderBtn) {
        cancelReaderBtn.addEventListener('click', function() {
            readerSettingsModal.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            event.target.style.display = 'none';
        }
    });

    // Notification function
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add to page
        document.body.appendChild(notification);

        // Show notification
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);

        // Close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }
});
